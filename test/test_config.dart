/// Test configuration and utilities for DassoShu Reader
/// 
/// This file provides common test setup, mocks, and utilities
/// used across all test files in the project.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/config/design_system.dart';

/// Creates a test widget wrapper with necessary providers and localization
Widget createTestWidget(Widget child, {List<Override>? overrides}) {
  return ProviderScope(
    overrides: overrides ?? [],
    child: MaterialApp(
      localizationsDelegates: L10n.localizationsDelegates,
      supportedLocales: L10n.supportedLocales,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: DesignSystem.primaryColor),
      ),
      home: Scaffold(body: child),
    ),
  );
}

/// Test utilities for common operations
class TestUtils {
  /// Simulates a delay for async operations
  static Future<void> delay([int milliseconds = 100]) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }

  /// Creates a mock file path for testing
  static String mockFilePath(String filename) {
    return '/test/mock/files/$filename';
  }

  /// Creates mock EPUB content for testing
  static String mockEpubContent({
    String title = 'Test Book',
    String author = 'Test Author',
    String description = 'Test Description',
  }) {
    return '''
    <?xml version="1.0" encoding="UTF-8"?>
    <package xmlns="http://www.idpf.org/2007/opf" version="3.0">
      <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
        <dc:title>$title</dc:title>
        <dc:creator>$author</dc:creator>
        <dc:description>$description</dc:description>
        <dc:language>zh-CN</dc:language>
      </metadata>
    </package>
    ''';
  }

  /// Creates mock Chinese text for testing
  static String mockChineseText() {
    return '这是一个测试文本，包含中文字符。';
  }

  /// Creates mock HSK character data
  static Map<String, dynamic> mockHskCharacter({
    String character = '你',
    String pinyin = 'nǐ',
    String meaning = 'you',
    int level = 1,
  }) {
    return {
      'character': character,
      'pinyin': pinyin,
      'meaning': meaning,
      'level': level,
    };
  }
}

/// Mock providers for testing
class MockProviders {
  /// Creates a mock book list provider override
  static Override mockBookListProvider(List<dynamic> books) {
    // This would be implemented with actual provider overrides
    // when the specific providers are tested
    throw UnimplementedError('Mock providers to be implemented per test');
  }
}

/// Test groups for organizing tests
class TestGroups {
  static const String unit = 'Unit Tests';
  static const String widget = 'Widget Tests';
  static const String integration = 'Integration Tests';
  static const String performance = 'Performance Tests';
  static const String platform = 'Platform Tests';
  static const String accessibility = 'Accessibility Tests';
}

/// Test tags for filtering tests
class TestTags {
  static const String core = 'core';
  static const String ai = 'ai';
  static const String dictionary = 'dictionary';
  static const String epub = 'epub';
  static const String chinese = 'chinese';
  static const String hsk = 'hsk';
  static const String ui = 'ui';
  static const String platform = 'platform';
  static const String performance = 'performance';
  static const String slow = 'slow';
}

/// Performance test utilities
class PerformanceTestUtils {
  /// Measures widget build time
  static Future<Duration> measureBuildTime(WidgetTester tester, Widget widget) async {
    final stopwatch = Stopwatch()..start();
    await tester.pumpWidget(widget);
    stopwatch.stop();
    return stopwatch.elapsed;
  }

  /// Measures async operation time
  static Future<Duration> measureAsyncOperation(Future<void> Function() operation) async {
    final stopwatch = Stopwatch()..start();
    await operation();
    stopwatch.stop();
    return stopwatch.elapsed;
  }

  /// Performance thresholds
  static const Duration maxWidgetBuildTime = Duration(milliseconds: 16); // 60 FPS
  static const Duration maxAsyncOperationTime = Duration(seconds: 3);
  static const int maxMemoryUsageMB = 150;
}

/// Accessibility test utilities
class AccessibilityTestUtils {
  /// Checks if widget has proper semantic labels
  static void checkSemanticLabels(WidgetTester tester) {
    final semantics = tester.binding.pipelineOwner.semanticsOwner;
    expect(semantics, isNotNull);
    // Additional semantic checks would be implemented here
  }

  /// Verifies minimum touch target size (44dp)
  static void checkTouchTargetSize(WidgetTester tester, Finder finder) {
    final widget = tester.widget(finder);
    final renderBox = tester.renderObject(finder) as RenderBox;
    final size = renderBox.size;
    
    expect(size.width, greaterThanOrEqualTo(44.0));
    expect(size.height, greaterThanOrEqualTo(44.0));
  }
}
