import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/config/design_system.dart';
import '../../test/test_config.dart';

void main() {
  group(TestGroups.widget, () {
    group('Chinese Font Rendering', () {
      testWidgets('should render Chinese characters correctly', (WidgetTester tester) async {
        final chineseText = TestUtils.mockChineseText();
        
        await tester.pumpWidget(
          createTestWidget(
            Text(
              chineseText,
              style: const TextStyle(
                fontFamily: 'NotoSansSC',
                fontSize: 16,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify Chinese text is displayed
        expect(find.text(chineseText), findsOneWidget);
      }, tags: [TestTags.chinese, TestTags.ui]);

      testWidgets('should use proper Chinese font family', (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            Text(
              '中文测试',
              style: TextStyle(
                fontFamily: 'NotoSansSC',
                fontSize: DesignSystem.fontSizeM,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final textWidget = tester.widget<Text>(find.byType(Text));
        expect(textWidget.style?.fontFamily, equals('NotoSansSC'));
      }, tags: [TestTags.chinese, TestTags.ui]);

      testWidgets('should handle mixed Chinese and English text', (WidgetTester tester) async {
        const mixedText = 'Hello 你好 World 世界';
        
        await tester.pumpWidget(
          createTestWidget(
            const Text(
              mixedText,
              style: TextStyle(
                fontFamily: 'NotoSansSC',
                fontSize: 16,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text(mixedText), findsOneWidget);
      }, tags: [TestTags.chinese, TestTags.ui]);

      testWidgets('should render Chinese characters in different font weights', (WidgetTester tester) async {
        const chineseText = '中文字体测试';
        
        await tester.pumpWidget(
          createTestWidget(
            Column(
              children: [
                Text(
                  chineseText,
                  style: TextStyle(
                    fontFamily: 'NotoSansSC',
                    fontSize: DesignSystem.fontSizeM,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                Text(
                  chineseText,
                  style: TextStyle(
                    fontFamily: 'NotoSansSC',
                    fontSize: DesignSystem.fontSizeM,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text(chineseText), findsNWidgets(2));
      }, tags: [TestTags.chinese, TestTags.ui]);

      testWidgets('should handle Chinese punctuation correctly', (WidgetTester tester) async {
        const chineseTextWithPunctuation = '你好，世界！这是一个测试。';
        
        await tester.pumpWidget(
          createTestWidget(
            Text(
              chineseTextWithPunctuation,
              style: const TextStyle(
                fontFamily: 'NotoSansSC',
                fontSize: 16,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text(chineseTextWithPunctuation), findsOneWidget);
      }, tags: [TestTags.chinese, TestTags.ui]);

      testWidgets('should render Chinese text in different sizes', (WidgetTester tester) async {
        const chineseText = '中文大小测试';
        
        await tester.pumpWidget(
          createTestWidget(
            Column(
              children: [
                Text(
                  chineseText,
                  style: TextStyle(
                    fontFamily: 'NotoSansSC',
                    fontSize: DesignSystem.fontSizeS,
                  ),
                ),
                Text(
                  chineseText,
                  style: TextStyle(
                    fontFamily: 'NotoSansSC',
                    fontSize: DesignSystem.fontSizeM,
                  ),
                ),
                Text(
                  chineseText,
                  style: TextStyle(
                    fontFamily: 'NotoSansSC',
                    fontSize: DesignSystem.fontSizeL,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text(chineseText), findsNWidgets(3));
      }, tags: [TestTags.chinese, TestTags.ui]);

      testWidgets('should maintain readability with Chinese text', (WidgetTester tester) async {
        const longChineseText = '''
        这是一段很长的中文文本，用来测试中文字体的可读性和渲染效果。
        文本包含多个句子，以确保在不同情况下都能正确显示中文字符。
        测试内容包括常用汉字、标点符号和数字123。
        ''';
        
        await tester.pumpWidget(
          createTestWidget(
            Container(
              width: 300,
              padding: EdgeInsets.all(DesignSystem.spaceM),
              child: const Text(
                longChineseText,
                style: TextStyle(
                  fontFamily: 'NotoSansSC',
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text(longChineseText), findsOneWidget);
      }, tags: [TestTags.chinese, TestTags.ui]);
    });
  });
}
