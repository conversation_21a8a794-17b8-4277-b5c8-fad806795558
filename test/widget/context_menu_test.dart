import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/widgets/context_menu/unified_context_menu.dart';
import '../../test/test_config.dart';

void main() {
  group(TestGroups.widget, () {
    group('Unified Context Menu', () {
      testWidgets('should display context menu with proper icons', (WidgetTester tester) async {
        // Test context menu display
        await tester.pumpWidget(
          createTestWidget(
            const UnifiedContextMenu(
              selectedText: 'Test text',
              onDictionaryLookup: null,
              onTranslate: null,
              onCopy: null,
              onHighlight: null,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify context menu is displayed
        expect(find.byType(UnifiedContextMenu), findsOneWidget);
      }, tags: [TestTags.ui, TestTags.core]);

      testWidgets('should handle Chinese text selection', (WidgetTester tester) async {
        final chineseText = TestUtils.mockChineseText();
        
        await tester.pumpWidget(
          createTestWidget(
            UnifiedContextMenu(
              selectedText: chineseText,
              onDictionaryLookup: null,
              onTranslate: null,
              onCopy: null,
              onHighlight: null,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify Chinese text is handled correctly
        expect(find.text(chineseText), findsOneWidget);
      }, tags: [TestTags.ui, TestTags.chinese]);

      testWidgets('should have proper touch target sizes', (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const UnifiedContextMenu(
              selectedText: 'Test text',
              onDictionaryLookup: null,
              onTranslate: null,
              onCopy: null,
              onHighlight: null,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Check touch target sizes for accessibility
        final iconButtons = find.byType(IconButton);
        for (int i = 0; i < iconButtons.evaluate().length; i++) {
          AccessibilityTestUtils.checkTouchTargetSize(tester, iconButtons.at(i));
        }
      }, tags: [TestTags.ui, TestTags.accessibility]);

      testWidgets('should display tooltips on icons', (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const UnifiedContextMenu(
              selectedText: 'Test text',
              onDictionaryLookup: null,
              onTranslate: null,
              onCopy: null,
              onHighlight: null,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Test tooltip display
        final iconButton = find.byType(IconButton).first;
        await tester.longPress(iconButton);
        await tester.pumpAndSettle();

        // Verify tooltip is shown
        expect(find.byType(Tooltip), findsWidgets);
      }, tags: [TestTags.ui, TestTags.accessibility]);

      testWidgets('should handle callback functions correctly', (WidgetTester tester) async {
        bool dictionaryLookupCalled = false;
        bool translateCalled = false;
        bool copyCalled = false;
        bool highlightCalled = false;

        await tester.pumpWidget(
          createTestWidget(
            UnifiedContextMenu(
              selectedText: 'Test text',
              onDictionaryLookup: () => dictionaryLookupCalled = true,
              onTranslate: () => translateCalled = true,
              onCopy: () => copyCalled = true,
              onHighlight: () => highlightCalled = true,
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Test callback functions
        final iconButtons = find.byType(IconButton);
        if (iconButtons.evaluate().isNotEmpty) {
          await tester.tap(iconButtons.first);
          await tester.pumpAndSettle();
          
          // At least one callback should be called
          expect(
            dictionaryLookupCalled || translateCalled || copyCalled || highlightCalled,
            isTrue,
          );
        }
      }, tags: [TestTags.ui, TestTags.core]);

      testWidgets('should build within performance threshold', (WidgetTester tester) async {
        final buildTime = await PerformanceTestUtils.measureBuildTime(
          tester,
          createTestWidget(
            const UnifiedContextMenu(
              selectedText: 'Test text',
              onDictionaryLookup: null,
              onTranslate: null,
              onCopy: null,
              onHighlight: null,
            ),
          ),
        );

        expect(buildTime, lessThan(PerformanceTestUtils.maxWidgetBuildTime));
      }, tags: [TestTags.ui, TestTags.performance]);
    });
  });
}
