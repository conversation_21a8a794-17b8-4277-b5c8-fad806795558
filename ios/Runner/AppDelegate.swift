import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
  private let CPU_CHANNEL = "dasso_reader/cpu"
  private let MEMORY_CHANNEL = "dasso_reader/memory"

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    guard let controller = window?.rootViewController as? FlutterViewController else {
      fatalError("rootViewController is not type FlutterViewController")
    }

    // Setup CPU monitoring channel
    let cpuChannel = FlutterMethodChannel(name: CPU_CHANNEL, binaryMessenger: controller.binaryMessenger)
    cpuChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "getCPUInfo":
        self?.getCPUInfo(result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    }

    // Setup Memory monitoring channel
    let memoryChannel = FlutterMethodChannel(name: MEMORY_CHANNEL, binaryMessenger: controller.binaryMessenger)
    memoryChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "getMemoryInfo":
        self?.getMemoryInfo(result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // MARK: - CPU Monitoring

  private func getCPUInfo(result: @escaping FlutterResult) {
    do {
      let cpuUsage = try getCPUUsage()
      let cpuInfo: [String: Any] = [
        "cpuUsage": cpuUsage,
        "timestamp": Int64(Date().timeIntervalSince1970 * 1000)
      ]
      result(cpuInfo)
    } catch {
      result(FlutterError(code: "CPU_ERROR", message: "Failed to get CPU info: \(error.localizedDescription)", details: nil))
    }
  }

  private func getCPUUsage() throws -> Double {
    var info = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

    let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
      $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
        task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
      }
    }

    if kerr == KERN_SUCCESS {
      // Get CPU usage from task info
      // iOS doesn't provide direct CPU percentage like Android's /proc/stat
      // We'll use a combination of task info and system load

      var loadInfo = host_cpu_load_info()
      var loadCount = mach_msg_type_number_t(MemoryLayout<host_cpu_load_info>.size / MemoryLayout<integer_t>.size)

      let hostKerr = withUnsafeMutablePointer(to: &loadInfo) {
        $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
          host_statistics(mach_host_self(), HOST_CPU_LOAD_INFO, $0, &loadCount)
        }
      }

      if hostKerr == KERN_SUCCESS {
        let user = Double(loadInfo.cpu_ticks.0)
        let system = Double(loadInfo.cpu_ticks.1)
        let idle = Double(loadInfo.cpu_ticks.2)
        let nice = Double(loadInfo.cpu_ticks.3)

        let total = user + system + idle + nice
        if total > 0 {
          let usage = ((user + system + nice) / total) * 100.0
          return min(max(usage, 0.0), 100.0) // Clamp between 0-100
        }
      }

      // Fallback: estimate based on task info
      return min(5.0, 100.0) // Conservative fallback
    } else {
      throw NSError(domain: "CPUMonitorError", code: Int(kerr), userInfo: [NSLocalizedDescriptionKey: "Failed to get task info"])
    }
  }

  // MARK: - Memory Monitoring

  private func getMemoryInfo(result: @escaping FlutterResult) {
    do {
      let memoryInfo = try getMemoryUsage()
      result(memoryInfo)
    } catch {
      result(FlutterError(code: "MEMORY_ERROR", message: "Failed to get memory info: \(error.localizedDescription)", details: nil))
    }
  }

  private func getMemoryUsage() throws -> [String: Any] {
    var info = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

    let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
      $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
        task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
      }
    }

    guard kerr == KERN_SUCCESS else {
      throw NSError(domain: "MemoryMonitorError", code: Int(kerr), userInfo: [NSLocalizedDescriptionKey: "Failed to get task info"])
    }

    // Get app memory usage
    let usedMemoryBytes = info.resident_size
    let usedMemoryMB = Int(usedMemoryBytes / (1024 * 1024))

    // Get system memory info
    let physicalMemory = ProcessInfo.processInfo.physicalMemory
    let totalMemoryMB = Int(physicalMemory / (1024 * 1024))

    // Get available memory (approximation)
    var vmInfo = vm_statistics64()
    var vmCount = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)

    let vmKerr = withUnsafeMutablePointer(to: &vmInfo) {
      $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
        host_statistics64(mach_host_self(), HOST_VM_INFO64, $0, &vmCount)
      }
    }

    var availableMemoryMB = totalMemoryMB - usedMemoryMB // Fallback calculation

    if vmKerr == KERN_SUCCESS {
      let pageSize = vm_kernel_page_size
      let freePages = vmInfo.free_count
      let availableBytes = UInt64(freePages) * UInt64(pageSize)
      availableMemoryMB = Int(availableBytes / (1024 * 1024))
    }

    // Check memory pressure using iOS-specific API
    let memoryPressure = ProcessInfo.processInfo.thermalState != .nominal

    return [
      "usedMemoryMB": usedMemoryMB,
      "totalMemoryMB": totalMemoryMB,
      "availableMemoryMB": availableMemoryMB,
      "nativeHeapSizeMB": usedMemoryMB, // iOS doesn't separate native heap like Android
      "nativeHeapUsedMB": usedMemoryMB,
      "memoryPressure": memoryPressure,
      "timestamp": Int64(Date().timeIntervalSince1970 * 1000)
    ]
  }
}
