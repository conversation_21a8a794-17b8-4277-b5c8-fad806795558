import 'dart:convert';
import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/iap_page.dart';
import 'package:dasso_reader/providers/ai_chat.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/service/convert_to_epub/txt/convert_from_txt.dart';
import 'package:dasso_reader/service/iap_service.dart';
import 'package:dasso_reader/utils/env_var.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/utils/import_book.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/webView/webview_console_message.dart';
import 'package:dasso_reader/utils/webView/webview_initial_variable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html/parser.dart' as html_parser;

import 'book_player/book_player_server.dart';

HeadlessInAppWebView? headlessInAppWebView;

/// import book list and **delete file**
void importBookList(List<File> fileList, BuildContext context, WidgetRef ref) {
  final allowBookExtensions = ['epub', 'mobi', 'azw3', 'fb2', 'txt'];

  AnxLog.info('importBook fileList: ${fileList.toString()}');

  List<File> supportedFiles = fileList.where((file) {
    return allowBookExtensions.contains(file.path.split('.').last);
  }).toList();

  List<File> unsupportedFiles = fileList.where((file) {
    return !allowBookExtensions.contains(file.path.split('.').last);
  }).toList();

  // delete unsupported files
  for (var file in unsupportedFiles) {
    file.deleteSync();
  }

  Widget bookItem(String path, Widget icon) {
    return Row(
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: icon,
        ),
        Expanded(
          child: Text(
            path.split('/').last,
            style: TextStyle(
              fontWeight: FontWeight.w300,
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }

  showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      String currentHandlingFile = '';
      List<String> errorFiles = [];

      return StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(
              L10n.of(context).import_n_books_selected(fileList.length),
              style: TextStyle(
                color:
                    DesignSystem.getSettingsTextColor(context, isPrimary: true),
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L10n.of(context)
                        .import_support_types(allowBookExtensions.join(' / ')),
                    style: TextStyle(
                      color: DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: false,
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  if (unsupportedFiles.isNotEmpty)
                    Text(
                      L10n.of(context)
                          .import_n_books_not_support(unsupportedFiles.length),
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                  const SizedBox(height: 20),
                  for (var file in unsupportedFiles)
                    bookItem(file.path, const Icon(Icons.error)),
                  for (var file in supportedFiles)
                    file.path == currentHandlingFile
                        ? bookItem(
                            file.path,
                            Container(
                              padding: const EdgeInsets.all(3),
                              width: 20,
                              height: 20,
                              child: const CircularProgressIndicator(),
                            ),
                          )
                        : bookItem(
                            file.path,
                            errorFiles.contains(file.path)
                                ? const Icon(Icons.error)
                                : const Icon(Icons.done),
                          ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  for (var file in supportedFiles) {
                    file.deleteSync();
                  }
                },
                child: Text(
                  L10n.of(context).common_cancel,
                  style: TextStyle(
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: false,
                    ),
                  ),
                ),
              ),
              if (supportedFiles.isNotEmpty)
                TextButton(
                  onPressed: () async {
                    for (var file in supportedFiles) {
                      AnxToast.show(file.path.split('/').last);
                      setState(() {
                        currentHandlingFile = file.path;
                      });
                      // try {
                      await importBook(file, ref);
                      // } catch (e) {
                      //   setState(() {
                      //     errorFiles.add(file.path);
                      //   });
                      // }
                    }
                    Navigator.of(navigatorKey.currentContext!).pop('dialog');
                  },
                  child: Text(
                    L10n.of(context)
                        .import_import_n_books(supportedFiles.length),
                    style: TextStyle(
                      color: DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: true,
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      );
    },
  );
}

Future<void> importBook(File file, WidgetRef ref) async {
  if (file.path.split('.').last == 'txt') {
    final tempFile = await convertFromTxt(file);
    file.deleteSync();
    file = tempFile;
  }

  await getBookMetadata(file, ref: ref);
  ref.read(bookListProvider.notifier).refresh();
}

Future<void> pushToReadingPage(
  WidgetRef ref,
  BuildContext context,
  Book book, {
  String? cfi,
}) async {
  if (book.isDeleted) {
    AnxToast.show(L10n.of(context).book_deleted);
    return;
  }
  if (EnvVar.isAppStore) {
    if (!IAPService().isFeatureAvailable) {
      Navigator.of(context).push(
        CupertinoPageRoute<void>(
          builder: (context) => const IAPPage(),
        ),
      );
      return;
    }
  }
  ref.read(aiChatProvider.notifier).clear();
  await Navigator.push(
    context,
    CupertinoPageRoute<void>(
      builder: (context) => ReadingPage(
        key: readingPageKey,
        book: book,
        cfi: cfi,
      ),
    ),
  );
}

Future<void> openBook(BuildContext context, Book book, WidgetRef ref) async {
  await pushToReadingPage(ref, context, book);
}

void updateBookRating(Book book, double rating) {
  book.rating = rating;
  updateBook(book);
}

Future<void> resetBookCover(Book book) async {
  File file = File(book.fileFullPath);
  getBookMetadata(file);
}

Future<void> saveBook(
  File file,
  String title,
  String author,
  String description,
  String cover, {
  Book? provideBook,
}) async {
  final newBookName =
      '${title.length > 20 ? title.substring(0, 20) : title}-${DateTime.now().millisecondsSinceEpoch}'
          .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
          .replaceAll('\n', '')
          .replaceAll('\r', '')
          .trim();

  final extension = file.path.split('.').last;

  final dbFilePath = 'file/$newBookName.$extension';
  final filePath = getBasePath(dbFilePath);
  String? dbCoverPath = 'cover/$newBookName';
  // final coverPath = getBasePath(dbCoverPath);

  await file.copy(filePath);
  // remove cached file
  file.delete();

  dbCoverPath = await saveImageToLocal(cover, dbCoverPath);

  Book book = Book(
    id: provideBook != null ? provideBook.id : -1,
    title: title,
    coverPath: dbCoverPath,
    filePath: dbFilePath,
    lastReadPosition: '',
    readingPercentage: 0,
    author: author,
    isDeleted: false,
    rating: 0.0,
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );

  book.id = await insertBook(book);
  AnxToast.show('Import successful');
  headlessInAppWebView?.dispose();
  headlessInAppWebView = null;
  return;
}

Future<void> getBookMetadata(
  File file, {
  Book? book,
  WidgetRef? ref,
}) async {
  String serverFileName = Server().setTempFile(file);

  String cfi = '';

  String indexHtmlPath =
      'http://localhost:${Server().port}/foliate-js/index.html';

  String bookUrl = 'http://localhost:${Server().port}/$serverFileName';
  AnxLog.info('import start: book url: $bookUrl');
  AnxLog.info('Platform: ${Platform.operatingSystem}');
  AnxLog.info('Server port: ${Server().port}');
  AnxLog.info('Index HTML path: $indexHtmlPath');

  // iOS has issues with HeadlessInAppWebView, use different approach
  if (Platform.isIOS) {
    AnxLog.info('iOS detected: Using iOS-specific metadata extraction');
    await _getBookMetadataIOS(file, indexHtmlPath, bookUrl, cfi, ref);
    return;
  }

  // iOS-specific WebView settings for localhost access
  final initialSettings = InAppWebViewSettings(
    allowsInlineMediaPlayback: true,
    allowsBackForwardNavigationGestures: false,
    isInspectable: true, // Enable debugging
    mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
    // iOS-specific settings
    allowsLinkPreview: false,
    isFraudulentWebsiteWarningEnabled: false,
  );

  AnxLog.info('iOS WebView: Creating HeadlessInAppWebView...');
  AnxLog.info('iOS WebView: Platform check - isIOS: ${Platform.isIOS}');
  AnxLog.info(
    'iOS WebView: WebViewEnvironment: ${webViewEnvironment != null ? 'Available' : 'Null'}',
  );

  HeadlessInAppWebView webview = HeadlessInAppWebView(
    webViewEnvironment: webViewEnvironment,
    initialUrlRequest: URLRequest(url: WebUri(indexHtmlPath)),
    initialSettings: initialSettings,
    onWebViewCreated: (controller) async {
      AnxLog.info('iOS WebView: onWebViewCreated callback triggered');
    },
    onLoadStop: (controller, url) async {
      AnxLog.info('iOS WebView onLoadStop: $url');

      // Register JavaScript handlers immediately when WebView loads
      // This must happen BEFORE the JavaScript tries to call them
      AnxLog.info('iOS WebView: Registering onMetadata handler');
      controller.addJavaScriptHandler(
        handlerName: 'onMetadata',
        callback: (List<dynamic> args) async {
          AnxLog.info(
            'iOS WebView onMetadata callback triggered with data: ${args.toString()}',
          );
          final metadata = args[0] as Map<String, dynamic>;
          final title = metadata['title'] as String? ?? 'Unknown';
          final dynamic authorData = metadata['author'];
          final String author;

          if (authorData is String) {
            author = authorData;
          } else if (authorData is List) {
            author = authorData
                .map((dynamic authorItem) {
                  if (authorItem is String) {
                    return authorItem;
                  } else if (authorItem is Map<String, dynamic>) {
                    return authorItem['name'] as String? ?? '';
                  }
                  return '';
                })
                .where((String name) => name.isNotEmpty)
                .join(', ');
          } else {
            author = 'Unknown';
          }

          // base64 cover
          final cover = metadata['cover'] as String? ?? '';
          final description = metadata['description'] as String? ?? '';
          AnxLog.info(
            'iOS WebView: Saving book - Title: $title, Author: $author',
          );
          saveBook(file, title, author, description, cover);
          ref?.read(bookListProvider.notifier).refresh();
        },
      );
    },
    onReceivedError: (controller, request, error) async {
      AnxLog.severe(
        'iOS WebView onReceivedError: ${request.url}, type: ${error.type}, description: ${error.description}',
      );
    },
    onConsoleMessage: (controller, consoleMessage) {
      AnxLog.info(
        'iOS WebView console: ${consoleMessage.messageLevel.name}: ${consoleMessage.message}',
      );
      if (consoleMessage.message.contains('loadBook')) {
        // Trigger the JavaScript initialization after loadBook console message
        AnxLog.info(
          'iOS WebView: loadBook console message detected, initializing JavaScript',
        );
        webviewInitialVariable(controller, bookUrl, cfi, importing: true);
      }
      if (consoleMessage.messageLevel == ConsoleMessageLevel.ERROR) {
        headlessInAppWebView?.dispose();
        headlessInAppWebView = null;
        // throw Exception('Webview: ${consoleMessage.message}');
      }
      webviewConsoleMessage(controller, consoleMessage);
    },
  );

  await webview.dispose();
  await webview.run();
  headlessInAppWebView = webview;
  AnxLog.info('iOS WebView started, waiting for metadata...');
  AnxLog.info('iOS WebView configuration: indexHtmlPath=$indexHtmlPath');
  AnxLog.info('iOS WebView configuration: bookUrl=$bookUrl');
  AnxLog.info('iOS WebView configuration: serverPort=${Server().port}');

  // Test server accessibility
  try {
    final response = await HttpClient().getUrl(Uri.parse(indexHtmlPath));
    final httpResponse = await response.close();
    AnxLog.info(
      'iOS WebView: Server accessibility test - Status: ${httpResponse.statusCode}',
    );
  } catch (e) {
    AnxLog.severe('iOS WebView: Server accessibility test failed: $e');
  }

  // max 30s
  int count = 0;
  while (count < 300) {
    if (headlessInAppWebView == null) {
      AnxLog.info('iOS WebView metadata extraction completed successfully');
      return;
    }
    await Future<void>.delayed(const Duration(milliseconds: 100));
    count++;

    // Log progress every 5 seconds
    if (count % 50 == 0) {
      AnxLog.info('iOS WebView waiting for metadata... ${count / 10}s elapsed');
    }
  }

  AnxLog.severe('iOS WebView metadata extraction timeout after 30s');
  headlessInAppWebView?.dispose();
  headlessInAppWebView = null;
  throw Exception(
    'Import: Get book metadata timeout - iOS WebView failed to extract metadata',
  );
}

/// iOS-specific metadata extraction using a different approach
/// HeadlessInAppWebView has issues on iOS, so we use an alternative method
Future<void> _getBookMetadataIOS(
  File file,
  String indexHtmlPath,
  String bookUrl,
  String cfi,
  WidgetRef? ref,
) async {
  AnxLog.info('iOS: Starting iOS-specific metadata extraction');

  // Primary approach: Direct EPUB parsing (faster and more reliable on iOS)
  try {
    await _extractMetadataDirectly(file, ref);
    AnxLog.info('iOS: Direct metadata extraction completed successfully');
    return;
  } catch (e) {
    AnxLog.warning('iOS: Direct metadata extraction failed: $e');
    AnxLog.info('iOS: Attempting WebView fallback...');
  }

  // Fallback approach: Try WebView-based parsing (if direct parsing fails)
  try {
    await _tryWebViewMetadataExtraction(file, indexHtmlPath, bookUrl, cfi, ref);
    AnxLog.info('iOS: WebView fallback extraction completed successfully');
    return;
  } catch (e) {
    AnxLog.warning('iOS: WebView fallback extraction failed: $e');
  }

  // Final fallback: Basic book creation with filename
  AnxLog.info('iOS: Using basic book creation as final fallback');
  await _createBasicBook(file, ref);
}

/// Extract metadata directly from EPUB file without WebView
Future<void> _extractMetadataDirectly(File file, WidgetRef? ref) async {
  AnxLog.info('iOS: Attempting direct EPUB metadata extraction');

  try {
    final metadata = await _parseEpubMetadata(file);
    AnxLog.info(
      'iOS: Successfully extracted metadata - Title: ${metadata.title}, Author: ${metadata.author}',
    );

    await saveBook(
      file,
      metadata.title,
      metadata.author,
      metadata.description,
      metadata.cover,
    );
    ref?.read(bookListProvider.notifier).refresh();
    AnxLog.info('iOS: Book import completed successfully with full metadata');
  } catch (e) {
    AnxLog.severe('iOS: EPUB metadata parsing failed: $e');
    rethrow; // Let the caller handle fallback
  }
}

/// Try WebView-based metadata extraction as fallback for iOS
Future<void> _tryWebViewMetadataExtraction(
  File file,
  String indexHtmlPath,
  String bookUrl,
  String cfi,
  WidgetRef? ref,
) async {
  AnxLog.info('iOS: Attempting WebView-based metadata extraction as fallback');

  // iOS-specific WebView settings for localhost access
  final initialSettings = InAppWebViewSettings(
    allowsInlineMediaPlayback: true,
    allowsBackForwardNavigationGestures: false,
    isInspectable: true,
    mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
    allowsLinkPreview: false,
    isFraudulentWebsiteWarningEnabled: false,
  );

  HeadlessInAppWebView? webview;
  bool metadataExtracted = false;

  try {
    webview = HeadlessInAppWebView(
      webViewEnvironment: webViewEnvironment,
      initialUrlRequest: URLRequest(url: WebUri(indexHtmlPath)),
      initialSettings: initialSettings,
      onLoadStop: (controller, url) async {
        AnxLog.info('iOS WebView fallback onLoadStop: $url');

        controller.addJavaScriptHandler(
          handlerName: 'onMetadata',
          callback: (List<dynamic> args) async {
            try {
              final metadata = args[0] as Map<String, dynamic>;
              final title = metadata['title'] as String? ?? 'Unknown';
              final dynamic authorData = metadata['author'];
              final String author;

              if (authorData is String) {
                author = authorData;
              } else if (authorData is List) {
                author = authorData
                    .map((dynamic authorItem) {
                      if (authorItem is String) {
                        return authorItem;
                      } else if (authorItem is Map<String, dynamic>) {
                        return authorItem['name'] as String? ?? '';
                      }
                      return '';
                    })
                    .where((String name) => name.isNotEmpty)
                    .join(', ');
              } else {
                author = 'Unknown';
              }

              final cover = metadata['cover'] as String? ?? '';
              final description = metadata['description'] as String? ?? '';

              await saveBook(file, title, author, description, cover);
              ref?.read(bookListProvider.notifier).refresh();

              metadataExtracted = true;
              webview?.dispose();
              webview = null;

              AnxLog.info(
                'iOS: WebView fallback metadata extraction successful',
              );
            } catch (e) {
              AnxLog.severe(
                'iOS: WebView fallback metadata processing failed: $e',
              );
              webview?.dispose();
              webview = null;
              rethrow;
            }
          },
        );

        // Initialize the WebView with book data
        await webviewInitialVariable(controller, bookUrl, cfi, importing: true);
      },
      onReceivedError: (controller, request, error) async {
        AnxLog.severe('iOS WebView fallback error: ${error.description}');
        webview?.dispose();
        webview = null;
        throw Exception('WebView fallback failed: ${error.description}');
      },
    );

    await webview!.run();

    // Wait for metadata extraction with timeout (15 seconds for fallback)
    int count = 0;
    while (count < 150 && !metadataExtracted && webview != null) {
      await Future<void>.delayed(const Duration(milliseconds: 100));
      count++;
    }

    if (!metadataExtracted) {
      webview?.dispose();
      throw Exception('WebView fallback timeout after 15 seconds');
    }
  } catch (e) {
    webview?.dispose();
    AnxLog.severe('iOS: WebView fallback extraction failed: $e');
    rethrow;
  }
}

/// Create a basic book entry when metadata extraction fails
Future<void> _createBasicBook(File file, WidgetRef? ref) async {
  AnxLog.info('iOS: Creating basic book entry as fallback');

  final fileName = file.path.split('/').last;
  final title =
      fileName.replaceAll(RegExp(r'\.[^.]*$'), ''); // Remove extension
  const author = 'Unknown';
  const description = '';
  const cover = '';

  await saveBook(file, title, author, description, cover);
  ref?.read(bookListProvider.notifier).refresh();
}

/// EPUB metadata container
class EpubMetadata {
  final String title;
  final String author;
  final String description;
  final String cover;

  EpubMetadata({
    required this.title,
    required this.author,
    required this.description,
    required this.cover,
  });
}

/// Production-ready EPUB metadata parser for iOS
/// Extracts title, author, description, and cover from EPUB files
Future<EpubMetadata> _parseEpubMetadata(File file) async {
  AnxLog.info('iOS: Starting EPUB metadata parsing for ${file.path}');

  try {
    // Read EPUB file as ZIP archive
    final bytes = await file.readAsBytes();
    final archive = ZipDecoder().decodeBytes(bytes);

    // Find and parse container.xml to get OPF path
    final containerEntry = archive.findFile('META-INF/container.xml');
    if (containerEntry == null) {
      throw Exception('Invalid EPUB: Missing container.xml');
    }

    final containerXml = _decodeXmlContent(containerEntry.content as List<int>);
    final opfPath = _extractOpfPath(containerXml);
    AnxLog.info('iOS: Found OPF path: $opfPath');

    // Find and parse OPF file
    final opfEntry = archive.findFile(opfPath);
    if (opfEntry == null) {
      throw Exception('Invalid EPUB: Missing OPF file at $opfPath');
    }

    final opfXml = _decodeXmlContent(opfEntry.content as List<int>);
    final metadata = _parseOpfMetadata(opfXml);

    // Extract cover image if available
    String coverBase64 = '';
    try {
      final coverPath = _findCoverPath(opfXml, opfPath);
      if (coverPath.isNotEmpty) {
        coverBase64 = await _extractCoverImage(archive, coverPath);
        AnxLog.info('iOS: Successfully extracted cover image');
      }
    } catch (e) {
      AnxLog.warning('iOS: Failed to extract cover image: $e');
    }

    final result = EpubMetadata(
      title: metadata['title'] ?? 'Unknown',
      author: metadata['author'] ?? 'Unknown',
      description: metadata['description'] ?? '',
      cover: coverBase64,
    );

    AnxLog.info('iOS: EPUB metadata parsing completed successfully');
    return result;
  } catch (e) {
    AnxLog.severe('iOS: EPUB metadata parsing failed: $e');
    rethrow;
  }
}

/// Extract OPF file path from container.xml
String _extractOpfPath(String containerXml) {
  try {
    final doc = html_parser.parse(containerXml);
    final rootfile = doc.querySelector('rootfile');
    final fullPath = rootfile?.attributes['full-path'];

    if (fullPath == null || fullPath.isEmpty) {
      throw Exception('No OPF path found in container.xml');
    }

    return fullPath;
  } catch (e) {
    throw Exception('Failed to parse container.xml: $e');
  }
}

/// Parse metadata from OPF XML content
Map<String, String> _parseOpfMetadata(String opfXml) {
  try {
    final doc = html_parser.parse(opfXml);
    final metadata = <String, String>{};

    // Extract title - try multiple selectors for better compatibility
    var titleElement = doc.querySelector('dc\\:title');
    titleElement ??= doc.querySelector('title');
    titleElement ??= doc.querySelector('[name="title"]');
    titleElement ??= doc.querySelector('metadata title');

    String title = titleElement?.text.trim() ?? '';

    // If title is still empty, try searching by tag name directly
    if (title.isEmpty) {
      final titleElements = doc.querySelectorAll('*');
      for (final element in titleElements) {
        if (element.localName?.toLowerCase() == 'title' ||
            element.localName?.contains('title') == true) {
          title = element.text.trim();
          if (title.isNotEmpty) break;
        }
      }
    }

    metadata['title'] = title;

    // Extract author/creator - try multiple selectors
    var creatorElement = doc.querySelector('dc\\:creator');
    creatorElement ??= doc.querySelector('creator');
    creatorElement ??= doc.querySelector('[name="creator"]');
    creatorElement ??= doc.querySelector('[name="author"]');
    creatorElement ??= doc.querySelector('metadata creator');

    String author = creatorElement?.text.trim() ?? '';

    // If author is still empty, try searching by tag name directly
    if (author.isEmpty) {
      final authorElements = doc.querySelectorAll('*');
      for (final element in authorElements) {
        if (element.localName?.toLowerCase() == 'creator' ||
            element.localName?.contains('creator') == true ||
            element.localName?.toLowerCase() == 'author') {
          author = element.text.trim();
          if (author.isNotEmpty) break;
        }
      }
    }

    metadata['author'] = author;

    // Extract description - try multiple selectors
    var descriptionElement = doc.querySelector('dc\\:description');
    descriptionElement ??= doc.querySelector('description');
    descriptionElement ??= doc.querySelector('[name="description"]');
    descriptionElement ??= doc.querySelector('metadata description');

    String description = descriptionElement?.text.trim() ?? '';

    // If description is still empty, try searching by tag name directly
    if (description.isEmpty) {
      final descElements = doc.querySelectorAll('*');
      for (final element in descElements) {
        if (element.localName?.toLowerCase() == 'description' ||
            element.localName?.contains('description') == true) {
          description = element.text.trim();
          if (description.isNotEmpty) break;
        }
      }
    }

    metadata['description'] = description;

    AnxLog.info(
      'iOS: Parsed metadata - Title: "${metadata['title']}", Author: "${metadata['author']}"',
    );

    // Log additional debug info if metadata extraction seems incomplete
    if (title.isEmpty || author.isEmpty) {
      AnxLog.warning(
        'iOS: Incomplete metadata extraction - Title: "$title", Author: "$author"',
      );
      AnxLog.info(
        'iOS: OPF XML structure for debugging: ${opfXml.length > 500 ? '${opfXml.substring(0, 500)}...' : opfXml}',
      );
    }

    return metadata;
  } catch (e) {
    AnxLog.severe('iOS: Failed to parse OPF metadata: $e');
    throw Exception('Failed to parse OPF metadata: $e');
  }
}

/// Find cover image path from OPF manifest
String _findCoverPath(String opfXml, String opfPath) {
  try {
    final doc = html_parser.parse(opfXml);

    // Method 1: Look for cover-image property
    final coverItem = doc.querySelector('item[properties*="cover-image"]');
    if (coverItem != null) {
      final href = coverItem.attributes['href'];
      if (href != null) {
        return _resolvePath(href, opfPath);
      }
    }

    // Method 2: Look for meta name="cover"
    final coverMeta = doc.querySelector('meta[name="cover"]');
    if (coverMeta != null) {
      final content = coverMeta.attributes['content'];
      if (content != null) {
        final item = doc.querySelector('item[id="$content"]');
        final href = item?.attributes['href'];
        if (href != null) {
          return _resolvePath(href, opfPath);
        }
      }
    }

    // Method 3: Look for first image in manifest
    final imageItem = doc.querySelector('item[media-type^="image/"]');
    if (imageItem != null) {
      final href = imageItem.attributes['href'];
      if (href != null) {
        return _resolvePath(href, opfPath);
      }
    }

    return '';
  } catch (e) {
    AnxLog.warning('iOS: Failed to find cover path: $e');
    return '';
  }
}

/// Resolve relative path against OPF directory
String _resolvePath(String href, String opfPath) {
  final opfDir = opfPath.contains('/')
      ? opfPath.substring(0, opfPath.lastIndexOf('/'))
      : '';
  return opfDir.isEmpty ? href : '$opfDir/$href';
}

/// Extract cover image as base64 string
Future<String> _extractCoverImage(Archive archive, String coverPath) async {
  try {
    final coverEntry = archive.findFile(coverPath);
    if (coverEntry == null) {
      throw Exception('Cover image not found: $coverPath');
    }

    final imageBytes = coverEntry.content as List<int>;
    final base64String = base64Encode(imageBytes);

    // Determine MIME type from file extension
    final extension = coverPath.toLowerCase().split('.').last;
    String mimeType;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        mimeType = 'image/jpeg';
        break;
      case 'png':
        mimeType = 'image/png';
        break;
      case 'gif':
        mimeType = 'image/gif';
        break;
      case 'webp':
        mimeType = 'image/webp';
        break;
      default:
        mimeType = 'image/jpeg'; // Default fallback
    }

    return 'data:$mimeType;base64,$base64String';
  } catch (e) {
    throw Exception('Failed to extract cover image: $e');
  }
}

/// Decode XML content with proper Chinese character encoding support
String _decodeXmlContent(List<int> bytes) {
  try {
    // First, try UTF-8 decoding (most common for modern EPUBs)
    try {
      final utf8String = utf8.decode(bytes);
      // Check if the string contains valid Chinese characters
      if (_isValidChineseText(utf8String)) {
        AnxLog.info(
          'iOS: Successfully decoded XML as UTF-8 with Chinese characters',
        );
        return utf8String;
      }
      // If no Chinese characters but valid UTF-8, still use it
      return utf8String;
    } catch (e) {
      AnxLog.warning('iOS: UTF-8 decoding failed: $e');
    }

    // Fallback: Try to detect encoding from XML declaration
    final asciiString =
        String.fromCharCodes(bytes.take(200)); // First 200 bytes
    final encodingMatch = RegExp(
      r'encoding\s*=\s*["\x27]([^"\x27]+)["\x27]',
      caseSensitive: false,
    ).firstMatch(asciiString);

    if (encodingMatch != null) {
      final encoding = encodingMatch.group(1)?.toLowerCase();
      AnxLog.info('iOS: Detected XML encoding: $encoding');

      switch (encoding) {
        case 'utf-8':
        case 'utf8':
          return utf8.decode(bytes);
        case 'gb2312':
        case 'gbk':
        case 'gb18030':
          // For Chinese GB encodings, try UTF-8 first as many files are mislabeled
          try {
            final utf8Result = utf8.decode(bytes);
            if (_isValidChineseText(utf8Result)) {
              AnxLog.info('iOS: GB-labeled file successfully decoded as UTF-8');
              return utf8Result;
            }
          } catch (e) {
            AnxLog.warning('iOS: GB-labeled file UTF-8 decode failed: $e');
          }
          // Fallback to Latin-1 for GB encodings (not ideal but prevents crashes)
          return latin1.decode(bytes);
        case 'iso-8859-1':
        case 'latin-1':
          return latin1.decode(bytes);
        default:
          AnxLog.warning(
            'iOS: Unknown encoding $encoding, falling back to UTF-8',
          );
          return utf8.decode(bytes, allowMalformed: true);
      }
    }

    // Final fallback: UTF-8 with malformed character handling
    AnxLog.warning(
      'iOS: No encoding detected, using UTF-8 with malformed handling',
    );
    return utf8.decode(bytes, allowMalformed: true);
  } catch (e) {
    AnxLog.severe('iOS: All XML decoding methods failed: $e');
    // Last resort: Latin-1 (preserves all bytes)
    return latin1.decode(bytes);
  }
}

/// Check if text contains valid Chinese characters
bool _isValidChineseText(String text) {
  // Check for common Chinese character ranges
  final chineseRegex = RegExp(r'[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]');
  return chineseRegex.hasMatch(text);
}
